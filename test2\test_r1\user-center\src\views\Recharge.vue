<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const amount = ref(10)
const paymentMethod = ref('wechat')
const customAmount = ref('')
const showCustomInput = ref(false)

const rechargeOptions = [
  { label: '10元', value: 10 },
  { label: '20元', value: 20 },
  { label: '50元', value: 50 },
  { label: '100元', value: 100 },
  { label: '自定义', value: 'custom' }
]

const handleAmountChange = (value: number | string) => {
  if (value === 'custom') {
    showCustomInput.value = true
    customAmount.value = ''
  } else {
    showCustomInput.value = false
    amount.value = value as number
  }
}

const handleRecharge = () => {
  let rechargeAmount = amount.value
  
  if (showCustomInput.value && customAmount.value) {
    const num = parseFloat(customAmount.value)
    if (!isNaN(num) && num > 0) {
      rechargeAmount = num
    } else {
      alert('请输入有效的充值金额')
      return
    }
  }
  
  userStore.updateBalance(rechargeAmount)
  alert(`充值成功！¥${rechargeAmount.toFixed(2)} 已添加到您的账户`)
}
</script>

<template>
  <div class="recharge">
    <h2>账户充值</h2>
    
    <div class="recharge-card">
      <div class="balance-display">
        <span>当前余额：</span>
        <span class="balance-amount">¥{{ userStore.balance.toFixed(2) }}</span>
      </div>
      
      <div class="amount-section">
        <h3>选择充值金额</h3>
        <div class="amount-options">
          <el-radio-group v-model="amount" @change="handleAmountChange">
            <el-radio-button 
              v-for="option in rechargeOptions" 
              :key="option.value" 
              :label="option.value"
            >
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
          
          <div v-if="showCustomInput" class="custom-amount">
            <el-input 
              v-model="customAmount" 
              placeholder="输入充值金额" 
              type="number"
              style="width: 200px; margin-top: 15px"
            >
              <template #append>元</template>
            </el-input>
          </div>
        </div>
      </div>
      
      <div class="payment-section">
        <h3>选择支付方式</h3>
        <div class="payment-methods">
          <el-radio-group v-model="paymentMethod">
            <el-radio label="wechat" border>
              <div class="payment-option">
                <img src="../assets/wechat-pay.png" alt="微信支付" class="payment-icon" />
                <span>微信支付</span>
              </div>
            </el-radio>
            <el-radio label="alipay" border>
              <div class="payment-option">
                <img src="../assets/alipay.png" alt="支付宝" class="payment-icon" />
                <span>支付宝</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      
      <div class="action-section">
        <el-button type="primary" size="large" @click="handleRecharge">立即充值</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.recharge {
  padding: 20px;
}

.recharge-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  margin-top: 20px;
}

.balance-display {
  font-size: 18px;
  margin-bottom: 30px;
  text-align: center;
}

.balance-amount {
  font-size: 24px;
  font-weight: bold;
  color: #e6a23c;
}

.amount-section, .payment-section {
  margin-bottom: 30px;
}

.amount-options {
  margin-top: 15px;
}

.payment-methods {
  margin-top: 15px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;
}

.payment-icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.action-section {
  text-align: center;
  margin-top: 30px;
}

.custom-amount {
  margin-top: 15px;
}
</style>
