<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
</script>

<template>
  <div class="app-container">
    <header>
      <div class="logo">用户中心</div>
      <nav>
        <RouterLink to="/my-info">我的信息</RouterLink>
        <RouterLink to="/bill-manage">账单管理</RouterLink>
        <RouterLink to="/api-key-manage">API Key</RouterLink>
        <RouterLink to="/usage-info">用量信息</RouterLink>
        <RouterLink to="/recharge">充值</RouterLink>
      </nav>
    </header>

    <main>
      <RouterView />
    </main>

    <footer>
      <p>© 2023 用户中心 - 保留所有权利</p>
    </footer>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

header {
  background: #2c3e50;
  color: white;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
}

nav {
  display: flex;
  gap: 20px;
}

nav a {
  color: #ecf0f1;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background 0.3s;
}

nav a:hover {
  background: rgba(255, 255, 255, 0.1);
}

nav a.router-link-active {
  background: #42b983;
  color: white;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 10px 0;
  margin-top: auto;
}
</style>
