# 用户中心前端项目

## 项目概述

这是一个基于现代前端技术栈开发的用户中心管理系统，提供了用户信息管理、账单管理、API Key管理、用量统计和账户充值等核心功能。项目采用模块化设计和响应式布局，确保在各种设备上都有良好的用户体验。

## 技术栈

- **Vue 3** - 前端框架
- **Vite** - 构建工具
- **TypeScript** - 静态类型检查
- **Pinia** - 状态管理
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Sass/SCSS** - CSS预处理器

## 功能模块

1. **我的信息**
   - 显示用户昵称、手机号、账户ID
   - 支持修改昵称和手机号
   - 头像展示和更换功能
   - 账户余额显示和充值入口

2. **账单管理**
   - 查看全部账单、购买账单和消费账单
   - 按时间、类型和状态筛选账单
   - 收入/支出金额可视化区分

3. **API Key管理**
   - 创建新的API Key
   - 列表展示所有API Key
   - 支持显示/隐藏Key内容
   - 复制、编辑和删除功能

4. **用量信息**
   - 按日/周/月查看用量统计
   - 柱状图展示用量数据
   - 用量概览卡片（今日/本周/本月）

5. **充值功能**
   - 选择充值金额（10/20/50/100元或自定义）
   - 选择支付方式（微信/支付宝）
   - 模拟充值流程和余额更新

## 项目结构

```
user-center/
├── public/                # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 静态资源（图片等）
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   ├── store/             # Pinia状态管理
│   │   ├── user.ts        # 用户状态
│   │   └── apiKey.ts      # API Key状态
│   ├── views/             # 页面组件
│   │   ├── MyInfo.vue     # 我的信息
│   │   ├── BillManage.vue # 账单管理
│   │   ├── ApiKeyManage.vue # API Key管理
│   │   ├── UsageInfo.vue  # 用量信息
│   │   └── Recharge.vue   # 充值页面
│   ├── App.vue            # 主应用组件
│   └── main.ts            # 应用入口
├── .gitignore             # Git忽略配置
├── index.html             # HTML入口
├── package.json           # 项目依赖
├── tsconfig.json          # TypeScript配置
└── vite.config.ts         # Vite配置
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

开发服务器将启动在 [http://localhost:5173](http://localhost:5173)

### 生产构建

```bash
npm run build
```

构建后的文件将生成在 `dist` 目录中

### 预览生产版本

```bash
npm run preview
```

## 部署指南

### 1. 构建生产版本

```bash
npm run build
```

### 2. 部署静态文件

将 `dist` 目录中的文件部署到任何静态文件托管服务，如：

- Nginx
- Apache
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### 3. Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

## 项目特点

1. **现代化技术栈**：使用 Vue 3 Composition API + TypeScript 开发
2. **响应式设计**：适配桌面和移动设备
3. **状态管理**：使用 Pinia 实现集中式状态管理
4. **模块化架构**：功能模块清晰分离，便于维护和扩展
5. **交互友好**：丰富的可视化组件和用户反馈
6. **性能优化**：Vite 快速构建和热模块替换

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目仓库
2. 创建新分支 (`git checkout -b feature/your-feature`)
3. 提交代码 (`git commit -m 'Add some feature'`)
4. 推送分支 (`git push origin feature/your-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 [MIT 许可证](LICENSE)
